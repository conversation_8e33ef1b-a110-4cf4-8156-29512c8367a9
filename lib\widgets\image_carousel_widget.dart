import 'package:bibl/controllers/lesson_controller.dart';
import 'package:bibl/controllers/profile_controller.dart';
import 'package:bibl/res/style.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:carousel_slider/carousel_slider.dart';
import 'package:get/get.dart';
import 'package:shimmer/shimmer.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../models/lesson_model.dart';
import '../models/quiz_model.dart';

class MyImageCarousel extends StatefulWidget {
  final LessonModel? lesson;
  final QuizModel? quiz;
  final ShuffleQuizModel? shuffleQuiz;

  const MyImageCarousel({super.key, this.lesson, this.quiz, this.shuffleQuiz});

  @override
  State<MyImageCarousel> createState() => _MyImageCarouselState();
}

class _MyImageCarouselState extends State<MyImageCarousel>
    with SingleTickerProviderStateMixin {
  final CarouselSliderController _carouselController =
      CarouselSliderController();
  final ProfileController profileController = Get.find();
  final LessonController lessonController = Get.find();

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  int _current = 0;
  List<Widget> _carouselItems = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeCarousel();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.95,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _initializeCarousel() {
    if (widget.lesson != null) {
      final pages = widget.lesson!.pages ?? [];
      final validPages = pages
          .where((page) =>
              page.pagePhotoLink != null && page.pagePhotoLink!.isNotEmpty)
          .toList();

      _carouselItems = validPages.map((page) {
        return _buildEnhancedImageWidget(
            page.pagePhotoLink!, widget.lesson!.lessonId!);
      }).toList();

      if (_carouselItems.isEmpty) {
        _carouselItems = [_buildEnhancedPlaceholder()];
      }
    } else if (widget.shuffleQuiz != null) {
      final questions = widget.shuffleQuiz!.questionsList ?? [];
      final validQuestions = questions
          .where((question) =>
              question.qsImage != null && question.qsImage!.isNotEmpty)
          .toList();

      _carouselItems = validQuestions.map((question) {
        return _buildEnhancedImageWidget(
            question.qsImage!, widget.shuffleQuiz!.quizId!);
      }).toList();

      if (_carouselItems.isEmpty) {
        _carouselItems = [_buildEnhancedPlaceholder()];
      }
    } else if (widget.quiz != null) {
      if (widget.quiz!.quizImageLink != null &&
          widget.quiz!.quizImageLink!.isNotEmpty) {
        _carouselItems = [
          _buildEnhancedImageWidget(
              widget.quiz!.quizImageLink!, widget.quiz!.quizId!)
        ];
      } else {
        _carouselItems = [_buildEnhancedPlaceholder()];
      }
    }

    // Aggressive prefetching
    _prefetchAllImages();
  }

  void _prefetchAllImages() {
    if (widget.lesson != null) {
      final urls =
          widget.lesson!.pages?.map((p) => p.pagePhotoLink ?? '').toList() ??
              [];
      lessonController.prefetchImages(urls, widget.lesson!.lessonId!);
    } else if (widget.shuffleQuiz != null) {
      final urls = widget.shuffleQuiz!.questionsList
              ?.map((q) => q.qsImage ?? '')
              .toList() ??
          [];
      lessonController.prefetchImages(urls, widget.shuffleQuiz!.quizId!);
    } else if (widget.quiz != null && widget.quiz!.quizImageLink != null) {
      lessonController
          .prefetchImages([widget.quiz!.quizImageLink!], widget.quiz!.quizId!);
    }
  }

  bool _isValidUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.hasAbsolutePath &&
          (uri.scheme == 'http' || uri.scheme == 'https');
    } catch (e) {
      return false;
    }
  }

  Widget _buildEnhancedImageWidget(String imageUrl, String prefix) {
    if (imageUrl.isEmpty || !_isValidUrl(imageUrl)) {
      return _buildEnhancedPlaceholder();
    }

    return ClipRRect(
      borderRadius: BorderRadius.circular(14),
      child: Stack(
        fit: StackFit.expand,
        children: [
          Obx(() => CachedNetworkImage(
                imageUrl: imageUrl,
                cacheManager: lessonController.cacheManager,
                fit: BoxFit.cover,
                cacheKey: lessonController.categoriesChanged.value
                    ? '${imageUrl}_${DateTime.now().millisecondsSinceEpoch}'
                    : null,
                fadeInDuration: const Duration(milliseconds: 400),
                fadeInCurve: Curves.easeOut,
                placeholder: (context, url) => _buildShimmerPlaceholder(),
                errorWidget: (context, url, error) =>
                    _buildEnhancedPlaceholder(),
              )),
          // Premium gradient overlay
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.05),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShimmerPlaceholder() {
    return Shimmer.fromColors(
      baseColor: Colors.grey[300]!,
      highlightColor: Colors.grey[100]!,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(14),
        ),
      ),
    );
  }

  Widget _buildEnhancedPlaceholder() {
    return TweenAnimationBuilder<double>(
      tween: Tween(begin: 0.0, end: 1.0),
      duration: const Duration(milliseconds: 600),
      curve: Curves.easeOut,
      builder: (context, value, child) {
        return Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(14),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                mainColor.withOpacity(0.05 * value),
                mainColor.withOpacity(0.1 * value),
              ],
            ),
            border: Border.all(
              color: mainColor.withOpacity(0.2 * value),
              width: 1,
            ),
          ),
          child: Opacity(
            opacity: value.clamp(0.0, 1.0),
            child: const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image_outlined,
                    size: 48,
                    color: mainColor,
                  ),
                  SizedBox(height: 8),
                  Text(
                    'Slika nije dostupna',
                    style: TextStyle(
                      color: mainColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: ScaleTransition(
        scale: _scaleAnimation,
        child: Column(
          children: [
            if (widget.lesson != null || widget.shuffleQuiz != null)
              _buildEnhancedCarousel()
            else if (widget.quiz != null)
              _buildSingleQuizImage(),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedCarousel() {
    return Column(
      children: [
        CarouselSlider(
          carouselController: _carouselController,
          options: CarouselOptions(
            enlargeCenterPage: true,
            height: 360,
            viewportFraction: 1,
            autoPlay: false,
            enlargeStrategy: CenterPageEnlargeStrategy.scale,
            onPageChanged: (index, reason) {
              if (mounted) {
                setState(() => _current = index);
              }
            },
          ),
          items: _carouselItems.asMap().entries.map((entry) {
            return AnimatedContainer(
              duration: const Duration(milliseconds: 300),
              margin: EdgeInsets.symmetric(
                horizontal: entry.key == _current ? 0 : 4,
              ),
              child: entry.value,
            );
          }).toList(),
        ),
        const SizedBox(height: 16),
        TweenAnimationBuilder<double>(
          tween: Tween(begin: 0.0, end: 1.0),
          duration: const Duration(milliseconds: 800),
          curve: Curves.easeOut,
          builder: (context, value, child) {
            return Opacity(
              opacity: value.clamp(0.0, 1.0),
              child: Transform.translate(
                offset: Offset(0, (1 - value) * 10),
                child: AnimatedSmoothIndicator(
                  activeIndex: _current,
                  count: _carouselItems.length,
                  effect: ExpandingDotsEffect(
                    dotHeight: 8,
                    dotWidth: 8,
                    activeDotColor: mainColor,
                    dotColor: mainColor.withOpacity(0.3),
                    spacing: 6,
                  ),
                  onDotClicked: (index) {
                    _carouselController.animateToPage(
                      index,
                      duration: const Duration(milliseconds: 500),
                      curve: Curves.easeOutCubic,
                    );
                  },
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildSingleQuizImage() {
    return Container(
      height: 360,
      width: double.infinity,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(14),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: _carouselItems.first,
    );
  }
}
