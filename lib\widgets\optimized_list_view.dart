import 'package:flutter/material.dart';

/// Optimized list widget with automatic performance enhancements
class OptimizedListView extends StatefulWidget {
  final IndexedWidgetBuilder itemBuilder;
  final int itemCount;
  final ScrollController? controller;
  final Widget? loadingWidget;
  final VoidCallback? onLoadMore;
  final double? itemExtent;
  final bool shrinkWrap;
  final EdgeInsets? padding;
  final ScrollPhysics? physics;

  const OptimizedListView({
    Key? key,
    required this.itemBuilder,
    required this.itemCount,
    this.controller,
    this.loadingWidget,
    this.onLoadMore,
    this.itemExtent,
    this.shrinkWrap = false,
    this.padding,
    this.physics,
  }) : super(key: key);

  @override
  State<OptimizedListView> createState() => _OptimizedListViewState();
}

class _OptimizedListViewState extends State<OptimizedListView> {
  late ScrollController _scrollController;
  final _visibleItems = <int>{};
  bool _isLoadingMore = false;

  // Performance optimization parameters
  static const double _scrollThreshold = 200.0;
  static const int _cacheExtent = 500;

  @override
  void initState() {
    super.initState();
    _scrollController = widget.controller ?? ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _scrollController.dispose();
    }
    super.dispose();
  }

  void _onScroll() {
    // Load more logic
    if (widget.onLoadMore != null && !_isLoadingMore) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      final currentScroll = _scrollController.position.pixels;

      if (maxScroll - currentScroll <= _scrollThreshold) {
        _loadMore();
      }
    }
  }

  Future<void> _loadMore() async {
    if (_isLoadingMore) return;

    setState(() => _isLoadingMore = true);

    try {
      widget.onLoadMore?.call();
      await Future.delayed(const Duration(milliseconds: 100));
    } finally {
      if (mounted) {
        setState(() => _isLoadingMore = false);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (notification) {
        // Track visible items for analytics or preloading
        if (notification is ScrollUpdateNotification) {
          _updateVisibleItems();
        }
        return false;
      },
      child: ListView.builder(
        controller: _scrollController,
        itemCount: widget.itemCount + (_isLoadingMore ? 1 : 0),
        itemExtent: widget.itemExtent,
        shrinkWrap: widget.shrinkWrap,
        padding: widget.padding,
        physics: widget.physics ?? const AlwaysScrollableScrollPhysics(),
        cacheExtent: _cacheExtent.toDouble(),
        addAutomaticKeepAlives: true,
        addRepaintBoundaries: true,
        itemBuilder: (context, index) {
          // Show loading widget at the end
          if (_isLoadingMore && index == widget.itemCount) {
            return widget.loadingWidget ?? _defaultLoadingWidget();
          }

          // Wrap items with performance optimization
          return _OptimizedListItem(
            key: ValueKey(index),
            index: index,
            builder: widget.itemBuilder,
          );
        },
      ),
    );
  }

  void _updateVisibleItems() {
    // This can be used for analytics or preloading
    final renderObject = context.findRenderObject() as RenderBox?;
    if (renderObject == null) return;

    // Implementation depends on specific needs
  }

  Widget _defaultLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      alignment: Alignment.center,
      child: const CircularProgressIndicator(
        strokeWidth: 2,
      ),
    );
  }
}

/// Optimized list item with automatic performance enhancements
class _OptimizedListItem extends StatefulWidget {
  final int index;
  final IndexedWidgetBuilder builder;

  const _OptimizedListItem({
    Key? key,
    required this.index,
    required this.builder,
  }) : super(key: key);

  @override
  State<_OptimizedListItem> createState() => _OptimizedListItemState();
}

class _OptimizedListItemState extends State<_OptimizedListItem>
    with AutomaticKeepAliveClientMixin {
  bool _isVisible = false;

  @override
  bool get wantKeepAlive => _isVisible;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return VisibilityDetector(
      key: ValueKey('item_${widget.index}'),
      onVisibilityChanged: (info) {
        if (info.visibleFraction > 0 && !_isVisible) {
          setState(() => _isVisible = true);
        }
      },
      child: RepaintBoundary(
        child: widget.builder(context, widget.index),
      ),
    );
  }
}

/// Simple visibility detector widget
class VisibilityDetector extends StatefulWidget {
  final Widget child;
  final Function(VisibilityInfo) onVisibilityChanged;
  final Key key;

  const VisibilityDetector({
    required this.key,
    required this.child,
    required this.onVisibilityChanged,
  }) : super(key: key);

  @override
  State<VisibilityDetector> createState() => _VisibilityDetectorState();
}

class _VisibilityDetectorState extends State<VisibilityDetector> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      widget.onVisibilityChanged(VisibilityInfo(visibleFraction: 1.0));
    });
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}

class VisibilityInfo {
  final double visibleFraction;
  VisibilityInfo({required this.visibleFraction});
}
